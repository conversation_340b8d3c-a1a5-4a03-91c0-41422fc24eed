import React, { createContext, useContext, useEffect } from "react";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { validateToken } from "./libs/user";
import { useLocalStorage } from "./hooks/useLocalStorage";

export const TokenContext = createContext<string>("");

interface ProvidersProps {
    children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
    const accessTokenKey = process.env.REACT_ACCESS_TOKEN_STORAGE || "access_token";
    const [token, setToken] = useLocalStorage(accessTokenKey, "");
    const [searchParams] = useSearchParams();
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const tokenQuery = searchParams.get("token") || "";

        try {
            if (tokenQuery && validateToken(tokenQuery)) {
                setToken(tokenQuery);
                // Remove token from URL after setting it
                navigate(location.pathname, { replace: true });
            } else if (token && !validateToken(token)) {
                // Token exists but is invalid, clear it and redirect
                setToken("");
                navigate("/401", { replace: true });
            } else if (!token && !tokenQuery && location.pathname !== "/401") {
                // No token available and not already on 401 page, redirect to 401
                navigate("/401", { replace: true });
            }
        } catch (error) {
            console.error("Token validation error:", error);
            // Clear invalid token
            setToken("");
            navigate("/401", { replace: true });
        }
    }, [searchParams, navigate, location.pathname, token, setToken]);

    return (
        <TokenContext.Provider value={token}>
            {children}
        </TokenContext.Provider>
    );

}

export const useAuth = () => {
    return useContext(TokenContext);
};
