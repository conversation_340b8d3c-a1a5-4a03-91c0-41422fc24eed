import { Link, useNavigate, useLocation } from "react-router-dom";

export default function NavBar() {
    const navigate = useNavigate();
    const location = useLocation();

    const handleBackClick = () => {
        navigate(-1);
    };

    // Don't show back button on home page
    const showBackButton = location.pathname !== '/';

    return (
        <nav className="flex items-center justify-between bg-blue-500 px-8 py-3">
            <div className="flex items-center gap-4">
                {showBackButton && (
                    <button
                        onClick={handleBackClick}
                        className="text-white hover:text-gray-200 transition-colors"
                        aria-label="Go back"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
                        </svg>
                    </button>
                )}
                <Link className="text-white font-bold" to={'/'}>Homestead</Link>
            </div>
            <Link className="bg-white p-2 flex rounded-lg" to={'/add-timeline'}>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg> Remind
            </Link>
        </nav>
    )
}